import React, { useState, useCallback, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import { ComponentType, FileData, GridItemData, Layout, ColumnSelection, DateFilter } from './types';
import { PanelFilter } from './FilterTypes';
import GridItem from './GridItem';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ReactGridLayout = WidthProvider(RGL);

interface GridLayoutProps {
  selectedFile: FileData | null;
  filteredData?: any; // Pre-filtered data
  createInitialPanels?: boolean;
  activePanels?: ComponentType[]; // Panels to restore from session storage
  onPanelsChange?: (activePanels: ComponentType[]) => void;

  // Selection and filter props
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;

  // Panel-specific filters
  panelFilters?: Record<string, PanelFilter[]>;
  conditionalFilters?: PanelFilter[];

  // Selection and filter handlers

  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void;
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string ,data?:any) => void;

  // Filter management handlers
  onAddFilter?: (filter: PanelFilter, panelId: string) => void;
  onRemoveFilter?: (filterId: string, panelId: string) => void;

  // View structure from database
  structure?: any;
}

const GridLayout = forwardRef<{getLayout: () => Layout, getItems: () => GridItemData[]}, GridLayoutProps>(({
  selectedFile,
  filteredData,
  createInitialPanels = false,
  activePanels = [],
  onPanelsChange,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  panelFilters = {},
  conditionalFilters = [],
  onColumnSelection,
  onDateFilterChange,
  onZoomSelection,
  onAddFilter,
  onRemoveFilter,
  structure
}, ref) => {
  const [items, setItems] = useState<GridItemData[]>([]);
  const [layout, setLayout] = useState<Layout>([]);
  const [nextId, setNextId] = useState(1);
  const gridContainerRef = useRef<HTMLDivElement>(null);

  // Custom drop handler for the grid container
  const handleContainerDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  }, []);

  const handleContainerDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const componentType = e.dataTransfer.getData('component') as ComponentType;
    console.log('Container drop event:', componentType);

    if (!componentType || !selectedFile) {
      console.log('Drop failed: ', !componentType ? 'No component type' : 'No selected file');
      return;
    }

    // Calculate drop position relative to the grid
    const rect = gridContainerRef.current?.getBoundingClientRect();
    if (!rect) {
      console.log('Drop failed: No grid container rect');
      return;
    }

    const x = Math.floor((e.clientX - rect.left) / 100); // Approximate column width
    const y = Math.floor((e.clientY - rect.top) / 60);  // Row height

    const newItem: GridItemData = {
      id: `item-${nextId}`,
      type: componentType,
      title: componentType === ComponentType.TimeSeriesPanel ? 'Time Series' :
             componentType === ComponentType.DataTablePanel ? 'Data Table' :
            //  componentType === ComponentType.HistogramPanel ? 'Histogram' :
             'Overview',
    };

    const newLayoutItem = {
      i: newItem.id,
      x: Math.max(0, Math.min(x, 8)), // Ensure it's within bounds
      y: Math.max(0, y),
      w: 6,
      h: 6,
      minW: 3,
      minH: 5,
    };

    setItems(prevItems => [...prevItems, newItem]);
    setLayout(prevLayout => [...prevLayout, newLayoutItem]);
    setNextId(prevId => prevId + 1);
  }, [nextId, selectedFile]);

  // Original RGL drop handler (as backup)
  const handleDrop = useCallback((_: Layout, layoutItem: any, event: any) => {
    const componentType = event.dataTransfer.getData('component') as ComponentType;
    console.log('RGL Drop event:', componentType, layoutItem);

    if (!componentType || !selectedFile) return;

    const newItem: GridItemData = {
      id: `item-${nextId}`,
      type: componentType,
      title: componentType === ComponentType.TimeSeriesPanel ? 'Time Series' :
             componentType === ComponentType.DataTablePanel ? 'Data Table' :
             componentType === ComponentType.HistogramPanel ? 'Histogram' :
             'Overview',
    };

    const newLayoutItem = {
      ...layoutItem,
      i: newItem.id,
      w: 6,
      h: 6,
      minW: 3,
      minH: 5,
    };

    setItems(prevItems => [...prevItems, newItem]);
    setLayout(prevLayout => [...prevLayout.filter(item => item.i !== '__dropping-elem__' && item.i !== newLayoutItem.i), newLayoutItem]);
    setNextId(prevId => prevId + 1);
  }, [nextId, selectedFile]);

  const handleLayoutChange = (newLayout: Layout) => {
    setLayout(newLayout);
  };

  // Notify parent component when panels change
  useEffect(() => {
    if (onPanelsChange) {
      const activePanelTypes = items.map(item => item.type);
      onPanelsChange(activePanelTypes);
    }
  }, [items, onPanelsChange]);

  // Create initial panels when a file is selected
  useEffect(() => {
    if (createInitialPanels && selectedFile) {
      console.log('Creating initial panels');

      // Clear existing items and layout
      setItems([]);
      setLayout([]);

      // Create Time Series panel
      const timeSeriesItem: GridItemData = {
        id: `item-${nextId}`,
        type: ComponentType.TimeSeriesPanel,
        title: 'Time Series',
      };

      // Create Overview panel
      const overviewItem: GridItemData = {
        id: `item-${nextId + 1}`,
        type: ComponentType.OverviewPanel,
        title: 'Overview',
      };

      // Create Histogram panel
      const histogramItem: GridItemData = {
        id: `item-${nextId + 2}`,
        type: ComponentType.HistogramPanel,
        title: 'Histogram',
      };

      // Create Data Table panel
      const tableItem: GridItemData = {
        id: `item-${nextId + 3}`,
        type: ComponentType.DataTablePanel,
        title: 'Data Table',
      };

      // Create layout for all panels
      const newLayout: Layout = [
        { i: timeSeriesItem.id, x: 0, y: 0, w: 6, h: 6, minW: 3, minH: 5 },
        { i: overviewItem.id, x: 6, y: 0, w: 6, h: 6, minW: 3, minH: 5 },
        { i: histogramItem.id, x: 0, y: 6, w: 6, h: 6, minW: 3, minH: 5 },
        { i: tableItem.id, x: 6, y: 6, w: 6, h: 6, minW: 3, minH: 5 },
      ];

      // Update state
      setItems([timeSeriesItem, overviewItem, histogramItem, tableItem]);
      setLayout(newLayout);
      setNextId(nextId + 4);
    }
  }, [createInitialPanels, selectedFile, nextId]);

  // Initialize layout and items from structure when it's provided
  useEffect(() => {
    if (structure && selectedFile) {
      console.log('Initializing from structure:', structure);

      // Clear existing items and layout
      setItems([]);
      setLayout([]);

      // Create items and layout from structure
      const newItems: GridItemData[] = [];
      const newLayout: Layout = [];
      let maxId = 0;

      // Process each item in the structure
      structure.forEach((layoutItem: any) => {
        // Extract the ID from the layout item
        const id = layoutItem.i;
        // Extract the numeric part of the ID for tracking the next ID
        const idNumber = parseInt(id.replace('item-', ''));
        if (idNumber > maxId) {
          maxId = idNumber;
        }

        // Create a new item based on the panel type in the structure
        const panelType = layoutItem.panelType;
        const newItem: GridItemData = {
          id: id,
          type: panelType,
          title: panelType === ComponentType.TimeSeriesPanel ? 'Time Series' :
                 panelType === ComponentType.DataTablePanel ? 'Data Table' :
                 panelType === ComponentType.HistogramPanel ? 'Histogram' :
                 'Overview',
        };

        // Create a layout item
        const newLayoutItem = {
          i: id,
          x: layoutItem.x,
          y: layoutItem.y,
          w: layoutItem.w,
          h: layoutItem.h,
          minW: layoutItem.minW || 3,
          minH: layoutItem.minH || 5,
        };

        newItems.push(newItem);
        newLayout.push(newLayoutItem);
      });

      // Update state
      setItems(newItems);
      setLayout(newLayout);
      setNextId(maxId + 1);

      console.log('Initialized items:', newItems);
      console.log('Initialized layout:', newLayout);
    }
  }, [structure, selectedFile]);

  // **SESSION STORAGE RESTORATION: Restore panels from activePanels prop**
  useEffect(() => {
    if (activePanels.length > 0 && selectedFile && items.length === 0 && !createInitialPanels) {
      console.log('🔄 Restoring panels from activePanels:', activePanels);

      // Clear existing items and layout
      setItems([]);
      setLayout([]);

      const newItems: GridItemData[] = [];
      const newLayout: Layout = [];
      let currentId = nextId;

      // Create panels based on activePanels array
      activePanels.forEach((panelType, index) => {
        const id = `item-${currentId}`;

        // Create grid item
        const newItem: GridItemData = {
          id: id,
          type: panelType,
          title: panelType === ComponentType.TimeSeriesPanel ? 'Time Series' :
                 panelType === ComponentType.DataTablePanel ? 'Data Table' :
                 panelType === ComponentType.HistogramPanel ? 'Histogram' :
                 'Overview',
        };

        // Create layout item with default positions (2x2 grid)
        const newLayoutItem = {
          i: id,
          x: (index % 2) * 6, // 0 or 6 (left or right column)
          y: Math.floor(index / 2) * 6, // 0 or 6 (top or bottom row)
          w: 6, // Half width
          h: 6, // Standard height
          minW: 3,
          minH: 5,
        };

        newItems.push(newItem);
        newLayout.push(newLayoutItem);
        currentId++;
      });

      // Update state
      setItems(newItems);
      setLayout(newLayout);
      setNextId(currentId);

      console.log('✅ Restored panels from session storage:', newItems);
    }
  }, [activePanels, selectedFile, items.length, createInitialPanels, nextId]);

  const handleRemoveItem = (id: string) => {
    console.log('Removing item:', id);

    // **ISSUE 3 FIX: Update activePanels and session storage when panel is removed**
    setItems(prevItems => {
      const updatedItems = prevItems.filter(item => item.id !== id);

      // Extract the panel types from the updated items
      const updatedActivePanels = updatedItems.map(item => item.type);

      // Update the parent component's activePanels state
      if (onPanelsChange) {
        onPanelsChange(updatedActivePanels);
      }

      console.log('🗑️ Panel removed, updated activePanels:', updatedActivePanels);

      return updatedItems;
    });

    setLayout(prevLayout => prevLayout.filter(item => item.i !== id));
  };

  const renderDropIndicator = () => {
    if (!selectedFile) {
      return (
        <div className="drop-indicator">
          Select a file first
        </div>
      );
    } else if (items.length === 0) {
      return (
        <div className="drop-indicator">
          Drag components from the sidebar and drop here
        </div>
      );
    }
    return null;
  };

  // Expose methods to the parent component
  useImperativeHandle(ref, () => ({
    getLayout: () => layout,
    getItems: () => items
  }));

  return (
    <div
      className="grid-container"
      ref={gridContainerRef}
      onDragOver={handleContainerDragOver}
      onDrop={handleContainerDrop}
      style={{ height: '100%', position: 'relative' }}
    >
      {renderDropIndicator()}
      {selectedFile && (
        <ReactGridLayout
          className="layout"
          layout={layout}
          cols={12}
          rowHeight={60}
          onLayoutChange={handleLayoutChange}
          onDrop={handleDrop}
          isDroppable={true}
          droppingItem={{ i: '__dropping-elem__', w: 4, h: 6 }}
          preventCollision={false}
          compactType="vertical"
          useCSSTransforms={true}
          allowOverlap={false}
          draggableHandle=".drag-handle"
          resizeHandles={['s', 'e', 'n', 'w', 'se', 'sw', 'ne', 'nw']} // Enable resizing from all sides
          // isBounded={true}
        >
          {items.map(item => {
            const itemLayout = layout.find(l => l.i === item.id);
            return (
              <div key={item.id} data-grid={itemLayout}>
                <GridItem
                  data={item}
                  file_id={selectedFile.csv_id}
                  fileData={selectedFile.data}
                  filteredData={filteredData}
                  onRemove={handleRemoveItem}
                  selectedColumns={selectedColumns}
                  dateFilter={dateFilter}
                  panelFilters={panelFilters[item.id] || []}
                  conditionalFilters={conditionalFilters}
                  onColumnSelection={onColumnSelection}
                  onDateFilterChange={onDateFilterChange}
                  onZoomSelection={onZoomSelection}
                  onAddFilter={onAddFilter ? (filter) => onAddFilter(filter, item.id) : undefined}
                  onRemoveFilter={onRemoveFilter ? (filterId) => onRemoveFilter(filterId, item.id) : undefined}
                  layout={itemLayout ? { x: itemLayout.x, y: itemLayout.y, w: itemLayout.w, h: itemLayout.h } : undefined}
                />
              </div>
            );
          })}
        </ReactGridLayout>
      )}
    </div>
  );
});

export default GridLayout;
